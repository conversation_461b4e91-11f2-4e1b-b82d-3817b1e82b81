<div data-bs-toggle="modal" data-bs-target="#campaignPhasesModal" style="cursor: pointer;">
@if ($influencerCampaignDetail->request == '1')
    <div class="row campaign-status-badge-container">
        <span class="campaign-status-badge" style="padding: 5px; background-color:#E8C11C; color:white;">
            Payment Phase
        </span>
    </div>
@else
    <div class="row campaign-status-badge-container">
        <span class="btn-request-phase campaign-status-badge">
            Request Phase
        </span>
    </div>
@endif
</div>
<div class="campaign-header">
    <div class="header-section">
        <div class="campagin_info" style="width: 60%;">
            <span style="color: #AD80FF; padding: 0 10px;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
            <h4 style="font-weight: 700;  padding: 0 10px;">{{ $influencerCampaignDetail->campaign_title }}</h4>
            <div>
                <span class="badge" style="margin-left: 0px !important;"><img
                        src="{{ asset('/assets/front-end/images/new/brand_camp.svg') }}"
                        width="20" height="20"> {{ $influencerCampaignDetail->user->first_name }}
                    {{ $influencerCampaignDetail->user->last_name }}</span>&nbsp;
                <span class="badge">
                    <img src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}"> {{ $influencerCampaignDetail->post_type }}
                </span>&nbsp;
                <span class="badge">
                    <img 
                        src="{{ asset('assets/front-end/images/icons/campaigns-' . $influencerCampaignDetail->media . '.svg') }}" 
                        alt="{{ ucfirst($influencerCampaignDetail->media) }} icon" 
                        width="20" 
                        height="20" 
                        loading="lazy" 
                        decoding="async" 
                        style="vertical-align:middle;"
                    >
                    {{ $influencerCampaignDetail->advertising }}
                </span>&nbsp;
            </div>
        </div>
        <div class="vertical-line"></div>
        <div class="details"
            style="width: 20%; display:flex; flex-direction:column;">
            <span class="text-success"
                style="font-size: 1.5em;font-weight: 700; padding: 5px;">
                @php
                    $newLivetreamPrice = 0;
                    $fieldName = $influencerCampaignDetail->advertising . '_price';
                    $user = App\Models\User::where('id', Auth::id())->first();
                    if ($user->advertisingMethodPrice != null) {
                        $newLivetreamPrice = $user->advertisingMethodPrice->$fieldName;
                    }
                @endphp
                € {{ number_format($influencerCampaignDetail->cash_out_amount ?? 0, 2) }}
            </span>
            <span class="submit-text-color" style="padding: 5px;">
                <span class="timing"
                    style="display:block; background-color:transparent; font-size:1.3em; width: 100%;"
                    id="timer{{ $influencerCampaignDetail->campaign_id }}">...</span>
                @if ($influencerCampaignDetail->request != '0' && $influencerCampaignDetail->request != '1')
                    <span class="smalltext" style="left: 0; bottom: 0; text-align: center; margin-top: 10px;">Your time to reply to the request</span>
                @elseif($influencerCampaignDetail->invoice_id == '')
                    <span class="smalltext" style="left: 0; bottom: 0; text-align: center; margin-top: 10px;">Waiting for payment</span>
                @endif
            </span>
        </div>
        <div class="vertical-line" style="margin-right: 0;"></div>
        <div class="details" class="details"
            style="width: 20%;  display:flex; flex-direction:column;">
            @if ($date <= $campaignDate)
                @if ($influencerCampaignDetail->request == '0')
                    <span class="reje">Rejected</span>
                @endif

                @if ($influencerCampaignDetail->request == '1')
                    <span class="acce" style="margin: 0; width:auto;">Accepted</span>
                @endif

                @if ($influencerCampaignDetail->request != '0' && $influencerCampaignDetail->request != '1')
                    <button
                        class="btn btn-show-details"
                        target="popup"
                        data-bs-toggle="modal"
                        data-bs-target="#influencerCheckRequestModal-{{ $influencerCampaignDetail->id }}">
                        Check Request
                    </button>
                @endif
            @else
                @if ($influencerCampaignDetail->request == '1')
                    <input type="button" class="table-btn light-red-btn" value="Payment Pending">
                @else
                    <span class="table-btn reje">Rejected</span>
                @endif
            @endif
        </div>
    </div>
</div>