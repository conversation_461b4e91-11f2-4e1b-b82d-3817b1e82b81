<div class="campaign-header">
    @if ($influencerCampaignDetail->request == '1')
        <div class="row" style="margin-top: -18px; margin-left:0px; margin-right: 0px; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#campaignPhasesModal">
            <span style="width: 100%; padding: 5px;display: flex; background-color:#E8C11C; color:white; justify-content: space-between;">
                <span style="text-align: left;">Payment Phase</span>
                <span style="text-align: right;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
            </span>
            <br>
        </div>
    @else
        <div class="row" style="margin-top: -18px; margin-left: 0px; margin-right: 0px; cursor: pointer;" data-bs-toggle="modal" data-bs-target="#campaignPhasesModal">
            <span class="btn-request-phase" style="width: 100%; padding: 5px; display: flex; justify-content: space-between;">
                <span style="text-align: left;">Request Phase</span>
                <span style="text-align: right;">ID # {{ $influencerCampaignDetail->campaign_id }}</span>
            </span>
            <br>
        </div>
    @endif
    <div class="header-section" style=" margin-bottom: 30px">
        <div class="row" style="padding-left:20px; width:100%;">
            <div class="col-9" style="padding-right: 0;">
                <h4 class="row truncate-text" style="font-weight: 700;font-size: 15px; padding-top: 10px; margin-left: -14px;">
                    {{ $influencerCampaignDetail->compaign_title }}
                </h4>
                <div class="row" style="width:100%">
                    <span class="col-auto"
                        style="font-size: 6px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/assets/front-end/images/new/three_users.svg') }}"
                            width="15" height="15">
                        {{ $influencerCampaignDetail->user->first_name }}
                        {{ $influencerCampaignDetail->user->last_name }}</span>
                    <span class="col-auto"
                        style="font-size: 6px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/assets/front-end/images/new/survey_camp.svg') }}"
                            width="15" height="15">
                        {{ $influencerCampaignDetail->post_type }}</span>
                    <span class="col-auto"
                        style="font-size: 6px !important; padding: 0 5px 0 0;"><img
                            src="{{ asset('/assets/front-end/images/icons/campaigns-' . $influencerCampaignDetail->media . '.svg') }}"
                            alt="" width="15" height="15">
                        {{ $influencerCampaignDetail->advertising }}</span>
                </div>
                <div class="row">
                    <span class="text-success col-sm px-0 mx-0"
                        style="font-size: 16px;font-weight: 700; width:35%;">
                        € {{ number_format($influencerCampaignDetail->cash_out_amount ?? 0, 2) }}
                    </span>
                    <span class="submit-text-color col-sm p-0 mx-0"
                        style="width:50%; line-height:10px">
                        <span class="timing"
                            style="width: auto; font-size: 0.78em; line-height: normal; margin-bottom: 0 !important; margin-left: 0 !important;"
                            id="timerMobile{{ $influencerCampaignDetail->campaign_id }}"></span>
                        @if ($influencerCampaignDetail->request != '0' && $influencerCampaignDetail->request != '1')
                            <span class="smalltext"
                                style="left: 0; bottom:0; text-align:left; font-size: 0.44em;">Your
                                time to reply to the request</span>
                        @elseif($influencerCampaignDetail->invoice_id == '')
                            <span class="smalltext"
                                style="left: 0; bottom:0; text-align:left; font-size: 0.44em;">Waiting
                                for payment</span>
                        @endif
                    </span>
                </div>
            </div>
            <div class="col-3"
                style="display:flex; align-items:center; justify-content:center; position: right; padding: 0 0px; margin: 0 0px;">
                @if ($date <= $campaignDate)
                    @if ($influencerCampaignDetail->request == '0')
                        <span class="reje">Rejected</span>
                    @endif

                    @if ($influencerCampaignDetail->request == '1')
                        <span class="acce">Accepted </span>
                    @endif

                    @if ($influencerCampaignDetail->request != '0' && $influencerCampaignDetail->request != '1')
                        <button class="btn  btn-show-details "
                            style="width: 100% !important; height: auto; padding: .375rem .75rem; font-size: 6px;"
                            target="popup" data-bs-toggle="modal"
                            data-bs-target="#influencerCheckRequestModal-{{ $influencerCampaignDetail->id }}">
                            Check Request
                        </button>
                    @endif
                @else
                    @if ($influencerCampaignDetail->request == '1')
                        <input type="button" class="table-btn light-red-btn" value="Payment Pending">
                    @else
                        <span class="table-btn reje">Rejected</span>
                    @endif
                @endif
            </div>
        </div>
    </div>
</div>